{"version": 3, "names": ["React", "Keyboard", "Platform", "useIsKeyboardShown", "isKeyboardShown", "setIsKeyboardShown", "useState", "useEffect", "handleKeyboardShow", "handleKeyboardHide", "subscriptions", "OS", "addListener", "for<PERSON>ach", "s", "remove"], "sourceRoot": "../../../src", "sources": ["utils/useIsKeyboardShown.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAmCC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AAE3E,OAAO,SAASC,kBAAkBA,CAAA,EAAG;EACnC,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGL,KAAK,CAACM,QAAQ,CAAC,KAAK,CAAC;EAEnEN,KAAK,CAACO,SAAS,CAAC,MAAM;IACpB,MAAMC,kBAAkB,GAAGA,CAAA,KAAMH,kBAAkB,CAAC,IAAI,CAAC;IACzD,MAAMI,kBAAkB,GAAGA,CAAA,KAAMJ,kBAAkB,CAAC,KAAK,CAAC;IAE1D,IAAIK,aAAoC;IAExC,IAAIR,QAAQ,CAACS,EAAE,KAAK,KAAK,EAAE;MACzBD,aAAa,GAAG,CACdT,QAAQ,CAACW,WAAW,CAAC,kBAAkB,EAAEJ,kBAAkB,CAAC,EAC5DP,QAAQ,CAACW,WAAW,CAAC,kBAAkB,EAAEH,kBAAkB,CAAC,CAC7D;IACH,CAAC,MAAM;MACLC,aAAa,GAAG,CACdT,QAAQ,CAACW,WAAW,CAAC,iBAAiB,EAAEJ,kBAAkB,CAAC,EAC3DP,QAAQ,CAACW,WAAW,CAAC,iBAAiB,EAAEH,kBAAkB,CAAC,CAC5D;IACH;IAEA,OAAO,MAAM;MACXC,aAAa,CAACG,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IAC1C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAOX,eAAe;AACxB", "ignoreList": []}