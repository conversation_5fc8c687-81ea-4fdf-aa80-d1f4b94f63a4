{"version": 3, "names": ["React", "useNavigation", "useFocusEffect", "effect", "navigation", "arguments", "undefined", "message", "console", "error", "useEffect", "isFocused", "cleanup", "callback", "destroy", "process", "env", "NODE_ENV", "then", "JSON", "stringify", "unsubscribeFocus", "addListener", "unsubscribeBlur"], "sourceRoot": "../../src", "sources": ["useFocusEffect.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,aAAa,QAAQ,oBAAiB;AAI/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,MAAsB,EAAE;EACrD,MAAMC,UAAU,GAAGH,aAAa,CAAC,CAAC;;EAElC;EACA,IAAII,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,EAAE;IAC9B,MAAMC,OAAO,GACX,sFAAsF,GACtF,8EAA8E,GAC9E,mBAAmB,GACnB,+BAA+B,GAC/B,yBAAyB,GACzB,sBAAsB,GACtB,QAAQ,GACR,oEAAoE;IAEtEC,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;EACxB;EAEAP,KAAK,CAACU,SAAS,CAAC,MAAM;IACpB,IAAIC,SAAS,GAAG,KAAK;IACrB,IAAIC,OAAwC;IAE5C,MAAMC,QAAQ,GAAGA,CAAA,KAAM;MACrB,MAAMC,OAAO,GAAGX,MAAM,CAAC,CAAC;MAExB,IAAIW,OAAO,KAAKR,SAAS,IAAI,OAAOQ,OAAO,KAAK,UAAU,EAAE;QAC1D,OAAOA,OAAO;MAChB;MAEA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIV,OAAO,GACT,6FAA6F;QAE/F,IAAIO,OAAO,KAAK,IAAI,EAAE;UACpBP,OAAO,IACL,kGAAkG;QACtG,CAAC,MAAM,IAAI,OAAQO,OAAO,CAASI,IAAI,KAAK,UAAU,EAAE;UACtDX,OAAO,IACL,uFAAuF,GACvF,uDAAuD,GACvD,8BAA8B,GAC9B,mBAAmB,GACnB,+BAA+B,GAC/B,oCAAoC,GACpC,+BAA+B,GAC/B,uDAAuD,GACvD,gBAAgB,GAChB,WAAW,GACX,oBAAoB,GACpB,kBAAkB,GAClB,QAAQ,GACR,oEAAoE;QACxE,CAAC,MAAM;UACLA,OAAO,IAAI,kBAAkBY,IAAI,CAACC,SAAS,CAACN,OAAO,CAAC,IAAI;QAC1D;QAEAN,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;MACxB;IACF,CAAC;;IAED;IACA,IAAIH,UAAU,CAACO,SAAS,CAAC,CAAC,EAAE;MAC1BC,OAAO,GAAGC,QAAQ,CAAC,CAAC;MACpBF,SAAS,GAAG,IAAI;IAClB;IAEA,MAAMU,gBAAgB,GAAGjB,UAAU,CAACkB,WAAW,CAAC,OAAO,EAAE,MAAM;MAC7D;MACA;MACA,IAAIX,SAAS,EAAE;QACb;MACF;MAEA,IAAIC,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,CAAC,CAAC;MACX;MAEAA,OAAO,GAAGC,QAAQ,CAAC,CAAC;MACpBF,SAAS,GAAG,IAAI;IAClB,CAAC,CAAC;IAEF,MAAMY,eAAe,GAAGnB,UAAU,CAACkB,WAAW,CAAC,MAAM,EAAE,MAAM;MAC3D,IAAIV,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,CAAC,CAAC;MACX;MAEAA,OAAO,GAAGN,SAAS;MACnBK,SAAS,GAAG,KAAK;IACnB,CAAC,CAAC;IAEF,OAAO,MAAM;MACX,IAAIC,OAAO,KAAKN,SAAS,EAAE;QACzBM,OAAO,CAAC,CAAC;MACX;MAEAS,gBAAgB,CAAC,CAAC;MAClBE,eAAe,CAAC,CAAC;IACnB,CAAC;EACH,CAAC,EAAE,CAACpB,MAAM,EAAEC,UAAU,CAAC,CAAC;AAC1B", "ignoreList": []}