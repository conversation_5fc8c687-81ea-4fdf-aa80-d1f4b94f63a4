{"version": 3, "names": ["PointerType", "isPointerInBounds", "view", "x", "y", "rect", "getBoundingClientRect", "left", "right", "top", "bottom", "PointerTypeMapping", "Map", "MOUSE", "TOUCH", "STYLUS", "OTHER", "degToRad", "degrees", "Math", "PI", "coneToDeviation", "cos", "calculateViewScale", "styles", "getComputedStyle", "resultScales", "scaleX", "scaleY", "scale", "undefined", "scales", "split", "parseFloat", "matrixElements", "RegExp", "exec", "transform", "matrixElementsArray", "tryExtractStylusData", "event", "pointerType", "get", "eventAzimuthAngle", "azimuthAngle", "eventAltitudeAngle", "altitudeAngle", "tiltX", "tiltY", "pressure", "spherical2tilt", "tilt2spherical", "tiltXrad", "tiltYrad", "abs", "tanX", "tan", "tanY", "atan2", "atan", "sqrt", "pow", "radToDeg", "tanAlt", "sin", "round", "RNSVGElements", "Set", "isRNSVGElement", "viewRef", "componentClassName", "Object", "getPrototypeOf", "constructor", "name", "has", "hasOwn", "isRNSVGNode", "node", "ref", "rngh", "type", "displayName"], "sourceRoot": "../../../src", "sources": ["web/utils.ts"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,gBAAgB;AAQ5C,OAAO,SAASC,iBAAiBA,CAACC,IAAiB,EAAE;EAAEC,CAAC;EAAEC;AAAS,CAAC,EAAW;EAC7E,MAAMC,IAAa,GAAGH,IAAI,CAACI,qBAAqB,CAAC,CAAC;EAElD,OAAOH,CAAC,IAAIE,IAAI,CAACE,IAAI,IAAIJ,CAAC,IAAIE,IAAI,CAACG,KAAK,IAAIJ,CAAC,IAAIC,IAAI,CAACI,GAAG,IAAIL,CAAC,IAAIC,IAAI,CAACK,MAAM;AAC/E;AAEA,OAAO,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAsB,CAC7D,CAAC,OAAO,EAAEZ,WAAW,CAACa,KAAK,CAAC,EAC5B,CAAC,OAAO,EAAEb,WAAW,CAACc,KAAK,CAAC,EAC5B,CAAC,KAAK,EAAEd,WAAW,CAACe,MAAM,CAAC,EAC3B,CAAC,MAAM,EAAEf,WAAW,CAACgB,KAAK,CAAC,CAC5B,CAAC;AAEF,OAAO,MAAMC,QAAQ,GAAIC,OAAe,IAAMA,OAAO,GAAGC,IAAI,CAACC,EAAE,GAAI,GAAG;AAEtE,OAAO,MAAMC,eAAe,GAAIH,OAAe,IAC7CC,IAAI,CAACG,GAAG,CAACL,QAAQ,CAACC,OAAO,GAAG,CAAC,CAAC,CAAC;AAEjC,OAAO,SAASK,kBAAkBA,CAACrB,IAAiB,EAAE;EACpD,MAAMsB,MAAM,GAAGC,gBAAgB,CAACvB,IAAI,CAAC;EAErC,MAAMwB,YAAY,GAAG;IACnBC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE;EACV,CAAC;;EAED;EACA,IAAIJ,MAAM,CAACK,KAAK,KAAKC,SAAS,IAAIN,MAAM,CAACK,KAAK,KAAK,MAAM,EAAE;IACzD,MAAME,MAAM,GAAGP,MAAM,CAACK,KAAK,CAACG,KAAK,CAAC,GAAG,CAAC;IAEtC,IAAID,MAAM,CAAC,CAAC,CAAC,EAAE;MACbL,YAAY,CAACC,MAAM,GAAGM,UAAU,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7C;IAEAL,YAAY,CAACE,MAAM,GAAGG,MAAM,CAAC,CAAC,CAAC,GAC3BE,UAAU,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC,GACrBE,UAAU,CAACF,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B;;EAEA;EACA,MAAMG,cAAc,GAAG,IAAIC,MAAM,CAAC,gBAAgB,CAAC,CAACC,IAAI,CACtDZ,MAAM,CAACa,SACT,CAAC,GAAG,CAAC,CAAC;EAEN,IAAIH,cAAc,EAAE;IAClB,MAAMI,mBAAmB,GAAGJ,cAAc,CAACF,KAAK,CAAC,IAAI,CAAC;IAEtDN,YAAY,CAACC,MAAM,IAAIM,UAAU,CAACK,mBAAmB,CAAC,CAAC,CAAC,CAAC;IACzDZ,YAAY,CAACE,MAAM,IAAIK,UAAU,CAACK,mBAAmB,CAAC,CAAC,CAAC,CAAC;EAC3D;EAEA,OAAOZ,YAAY;AACrB;AAEA,OAAO,SAASa,oBAAoBA,CAClCC,KAAmB,EACK;EACxB,MAAMC,WAAW,GAAG9B,kBAAkB,CAAC+B,GAAG,CAACF,KAAK,CAACC,WAAW,CAAC;EAE7D,IAAIA,WAAW,KAAKzC,WAAW,CAACe,MAAM,EAAE;IACtC;EACF;;EAEA;EACA,MAAM4B,iBAAqC,GAAGH,KAAK,CAACI,YAAY;EAChE;EACA,MAAMC,kBAAsC,GAAGL,KAAK,CAACM,aAAa;EAElE,IAAIN,KAAK,CAACO,KAAK,KAAK,CAAC,IAAIP,KAAK,CAACQ,KAAK,KAAK,CAAC,EAAE;IAC1C;IACA;;IAEA;IACA;IACA,IAAIL,iBAAiB,KAAKb,SAAS,IAAIe,kBAAkB,KAAKf,SAAS,EAAE;MACvE,OAAO;QACLiB,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRJ,YAAY,EAAEzB,IAAI,CAACC,EAAE,GAAG,CAAC;QACzB0B,aAAa,EAAE3B,IAAI,CAACC,EAAE,GAAG,CAAC;QAC1B6B,QAAQ,EAAET,KAAK,CAACS;MAClB,CAAC;IACH;IAEA,MAAM;MAAEF,KAAK;MAAEC;IAAM,CAAC,GAAGE,cAAc,CACrCL,kBAAkB,EAClBF,iBACF,CAAC;IAED,OAAO;MACLI,KAAK;MACLC,KAAK;MACLJ,YAAY,EAAED,iBAAiB;MAC/BG,aAAa,EAAED,kBAAkB;MACjCI,QAAQ,EAAET,KAAK,CAACS;IAClB,CAAC;EACH;EAEA,MAAM;IAAEH,aAAa;IAAEF;EAAa,CAAC,GAAGO,cAAc,CACpDX,KAAK,CAACO,KAAK,EACXP,KAAK,CAACQ,KACR,CAAC;EAED,OAAO;IACLD,KAAK,EAAEP,KAAK,CAACO,KAAK;IAClBC,KAAK,EAAER,KAAK,CAACQ,KAAK;IAClBJ,YAAY;IACZE,aAAa;IACbG,QAAQ,EAAET,KAAK,CAACS;EAClB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACJ,KAAa,EAAEC,KAAa,EAAE;EACpD,MAAMI,QAAQ,GAAIL,KAAK,GAAG5B,IAAI,CAACC,EAAE,GAAI,GAAG;EACxC,MAAMiC,QAAQ,GAAIL,KAAK,GAAG7B,IAAI,CAACC,EAAE,GAAI,GAAG;;EAExC;EACA,IAAIwB,YAAY,GAAG,CAAC;EAEpB,IAAIG,KAAK,KAAK,CAAC,EAAE;IACf,IAAIC,KAAK,GAAG,CAAC,EAAE;MACbJ,YAAY,GAAGzB,IAAI,CAACC,EAAE,GAAG,CAAC;IAC5B,CAAC,MAAM,IAAI4B,KAAK,GAAG,CAAC,EAAE;MACpBJ,YAAY,GAAI,CAAC,GAAGzB,IAAI,CAACC,EAAE,GAAI,CAAC;IAClC;EACF,CAAC,MAAM,IAAI4B,KAAK,KAAK,CAAC,EAAE;IACtB,IAAID,KAAK,GAAG,CAAC,EAAE;MACbH,YAAY,GAAGzB,IAAI,CAACC,EAAE;IACxB;EACF,CAAC,MAAM,IAAID,IAAI,CAACmC,GAAG,CAACP,KAAK,CAAC,KAAK,EAAE,IAAI5B,IAAI,CAACmC,GAAG,CAACN,KAAK,CAAC,KAAK,EAAE,EAAE;IAC3D;IACAJ,YAAY,GAAG,CAAC;EAClB,CAAC,MAAM;IACL;IACA,MAAMW,IAAI,GAAGpC,IAAI,CAACqC,GAAG,CAACJ,QAAQ,CAAC;IAC/B,MAAMK,IAAI,GAAGtC,IAAI,CAACqC,GAAG,CAACH,QAAQ,CAAC;IAE/BT,YAAY,GAAGzB,IAAI,CAACuC,KAAK,CAACD,IAAI,EAAEF,IAAI,CAAC;IACrC,IAAIX,YAAY,GAAG,CAAC,EAAE;MACpBA,YAAY,IAAI,CAAC,GAAGzB,IAAI,CAACC,EAAE;IAC7B;EACF;;EAEA;EACA,IAAI0B,aAAa,GAAG,CAAC;EAErB,IAAI3B,IAAI,CAACmC,GAAG,CAACP,KAAK,CAAC,KAAK,EAAE,IAAI5B,IAAI,CAACmC,GAAG,CAACN,KAAK,CAAC,KAAK,EAAE,EAAE;IACpDF,aAAa,GAAG,CAAC;EACnB,CAAC,MAAM,IAAIC,KAAK,KAAK,CAAC,EAAE;IACtBD,aAAa,GAAG3B,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGD,IAAI,CAACmC,GAAG,CAACD,QAAQ,CAAC;EAClD,CAAC,MAAM,IAAIL,KAAK,KAAK,CAAC,EAAE;IACtBF,aAAa,GAAG3B,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGD,IAAI,CAACmC,GAAG,CAACF,QAAQ,CAAC;EAClD,CAAC,MAAM;IACL;IACAN,aAAa,GAAG3B,IAAI,CAACwC,IAAI,CACvB,GAAG,GACDxC,IAAI,CAACyC,IAAI,CACPzC,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAACqC,GAAG,CAACJ,QAAQ,CAAC,EAAE,CAAC,CAAC,GAAGjC,IAAI,CAAC0C,GAAG,CAAC1C,IAAI,CAACqC,GAAG,CAACH,QAAQ,CAAC,EAAE,CAAC,CAClE,CACJ,CAAC;EACH;EAEA,OAAO;IAAEP,aAAa,EAAEA,aAAa;IAAEF,YAAY,EAAEA;EAAa,CAAC;AACrE;;AAEA;AACA;AACA;AACA,SAASM,cAAcA,CAACJ,aAAqB,EAAEF,YAAoB,EAAE;EACnE,MAAMkB,QAAQ,GAAG,GAAG,GAAG3C,IAAI,CAACC,EAAE;EAE9B,IAAIgC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAEhB,IAAIP,aAAa,KAAK,CAAC,EAAE;IACvB;IACA,IAAIF,YAAY,KAAK,CAAC,IAAIA,YAAY,KAAK,CAAC,GAAGzB,IAAI,CAACC,EAAE,EAAE;MACtD;MACAgC,QAAQ,GAAGjC,IAAI,CAACC,EAAE,GAAG,CAAC;IACxB;IACA,IAAIwB,YAAY,KAAKzB,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE;MAChC;MACAiC,QAAQ,GAAGlC,IAAI,CAACC,EAAE,GAAG,CAAC;IACxB;IACA,IAAIwB,YAAY,KAAKzB,IAAI,CAACC,EAAE,EAAE;MAC5B;MACAgC,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAE,GAAG,CAAC;IACzB;IACA,IAAIwB,YAAY,KAAM,CAAC,GAAGzB,IAAI,CAACC,EAAE,GAAI,CAAC,EAAE;MACtC;MACAiC,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAE,GAAG,CAAC;IACzB;IACA,IAAIwB,YAAY,GAAG,CAAC,IAAIA,YAAY,GAAGzB,IAAI,CAACC,EAAE,GAAG,CAAC,EAAE;MAClDgC,QAAQ,GAAGjC,IAAI,CAACC,EAAE,GAAG,CAAC;MACtBiC,QAAQ,GAAGlC,IAAI,CAACC,EAAE,GAAG,CAAC;IACxB;IACA,IAAIwB,YAAY,GAAGzB,IAAI,CAACC,EAAE,GAAG,CAAC,IAAIwB,YAAY,GAAGzB,IAAI,CAACC,EAAE,EAAE;MACxDgC,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAE,GAAG,CAAC;MACvBiC,QAAQ,GAAGlC,IAAI,CAACC,EAAE,GAAG,CAAC;IACxB;IACA,IAAIwB,YAAY,GAAGzB,IAAI,CAACC,EAAE,IAAIwB,YAAY,GAAI,CAAC,GAAGzB,IAAI,CAACC,EAAE,GAAI,CAAC,EAAE;MAC9DgC,QAAQ,GAAG,CAACjC,IAAI,CAACC,EAAE,GAAG,CAAC;MACvBiC,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAE,GAAG,CAAC;IACzB;IACA,IAAIwB,YAAY,GAAI,CAAC,GAAGzB,IAAI,CAACC,EAAE,GAAI,CAAC,IAAIwB,YAAY,GAAG,CAAC,GAAGzB,IAAI,CAACC,EAAE,EAAE;MAClEgC,QAAQ,GAAGjC,IAAI,CAACC,EAAE,GAAG,CAAC;MACtBiC,QAAQ,GAAG,CAAClC,IAAI,CAACC,EAAE,GAAG,CAAC;IACzB;EACF;EAEA,IAAI0B,aAAa,KAAK,CAAC,EAAE;IACvB,MAAMiB,MAAM,GAAG5C,IAAI,CAACqC,GAAG,CAACV,aAAa,CAAC;IAEtCM,QAAQ,GAAGjC,IAAI,CAACwC,IAAI,CAACxC,IAAI,CAACG,GAAG,CAACsB,YAAY,CAAC,GAAGmB,MAAM,CAAC;IACrDV,QAAQ,GAAGlC,IAAI,CAACwC,IAAI,CAACxC,IAAI,CAAC6C,GAAG,CAACpB,YAAY,CAAC,GAAGmB,MAAM,CAAC;EACvD;EAEA,MAAMhB,KAAK,GAAG5B,IAAI,CAAC8C,KAAK,CAACb,QAAQ,GAAGU,QAAQ,CAAC;EAC7C,MAAMd,KAAK,GAAG7B,IAAI,CAAC8C,KAAK,CAACZ,QAAQ,GAAGS,QAAQ,CAAC;EAE7C,OAAO;IAAEf,KAAK;IAAEC;EAAM,CAAC;AACzB;AAEA,OAAO,MAAMkB,aAAa,GAAG,IAAIC,GAAG,CAAC,CACnC,QAAQ,EACR,UAAU,EACV,SAAS,EACT,eAAe,EACf,GAAG,EACH,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,MAAM,EACN,UAAU,EACV,KAAK,CACN,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAACC,OAAmC,EAAE;EAClE,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,cAAc,CAACH,OAAO,CAAC,CAACI,WAAW,CAACC,IAAI;EAE1E,OACER,aAAa,CAACS,GAAG,CAACL,kBAAkB,CAAC,IACrCC,MAAM,CAACK,MAAM,CAACP,OAAO,EAAE,YAAY,CAAC;AAExC;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASQ,WAAWA,CAACC,IAAS,EAAE;EACrC;EACA;EACA,IAAIA,IAAI,CAACC,GAAG,EAAEC,IAAI,EAAE;IAClB,OAAO,KAAK;EACd;EAEA,OACET,MAAM,CAACC,cAAc,CAACM,IAAI,EAAEG,IAAI,CAAC,EAAEP,IAAI,KAAK,UAAU,IACtDR,aAAa,CAACS,GAAG,CAACG,IAAI,EAAEG,IAAI,EAAEC,WAAW,CAAC;AAE9C", "ignoreList": []}