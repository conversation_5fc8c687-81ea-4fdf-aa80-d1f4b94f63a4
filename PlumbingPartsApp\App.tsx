import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

export default function App() {
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Ionicons name="construct" size={80} color="#007AFF" />
        <Text style={styles.title}>Plumbing Parts App</Text>
        <Text style={styles.subtitle}>AI-Powered Part Identification</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.feature}>
          <Ionicons name="camera" size={40} color="#007AFF" />
          <Text style={styles.featureTitle}>Camera Recognition</Text>
          <Text style={styles.featureText}>
            Take photos to identify plumbing parts instantly
          </Text>
        </View>

        <View style={styles.feature}>
          <Ionicons name="search" size={40} color="#007AFF" />
          <Text style={styles.featureTitle}>Smart Search</Text>
          <Text style={styles.featureText}>
            Find parts by name, category, or specifications
          </Text>
        </View>

        <View style={styles.feature}>
          <Ionicons name="pricetag" size={40} color="#007AFF" />
          <Text style={styles.featureTitle}>Price Comparison</Text>
          <Text style={styles.featureText}>
            Compare prices across multiple suppliers
          </Text>
        </View>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity style={styles.primaryButton}>
          <Text style={styles.primaryButtonText}>Get Started</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.secondaryButton}>
          <Text style={styles.secondaryButtonText}>Learn More</Text>
        </TouchableOpacity>
      </View>

      <StatusBar style="auto" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
  },
  feature: {
    alignItems: 'center',
    marginBottom: 40,
    paddingHorizontal: 20,
  },
  featureTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  actions: {
    paddingBottom: 40,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#007AFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: '#007AFF',
    fontSize: 18,
    fontWeight: '600',
  },
});

