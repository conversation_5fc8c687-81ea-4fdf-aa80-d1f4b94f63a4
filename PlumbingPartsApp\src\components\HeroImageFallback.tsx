import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import colors from '../theme/colors';

const { width, height } = Dimensions.get('window');

interface HeroImageFallbackProps {
  style?: any;
  children?: React.ReactNode;
}

const HeroImageFallback: React.FC<HeroImageFallbackProps> = ({ style, children }) => {
  return (
    <View style={[styles.container, style]}>
      <LinearGradient
        colors={[colors.background.dark, colors.primary]}
        style={styles.gradient}
      >
        {/* Decorative plumbing icons */}
        <View style={styles.iconContainer}>
          <View style={[styles.iconWrapper, { top: '10%', left: '15%' }]}>
            <Ionicons name="water" size={24} color={colors.text.inverse} style={styles.icon} />
          </View>
          <View style={[styles.iconWrapper, { top: '20%', right: '20%' }]}>
            <Ionicons name="construct" size={20} color={colors.text.inverse} style={styles.icon} />
          </View>
          <View style={[styles.iconWrapper, { top: '35%', left: '10%' }]}>
            <Ionicons name="git-branch" size={18} color={colors.text.inverse} style={styles.icon} />
          </View>
          <View style={[styles.iconWrapper, { top: '45%', right: '15%' }]}>
            <Ionicons name="radio-button-on" size={22} color={colors.text.inverse} style={styles.icon} />
          </View>
          <View style={[styles.iconWrapper, { bottom: '25%', left: '20%' }]}>
            <Ionicons name="settings" size={26} color={colors.text.inverse} style={styles.icon} />
          </View>
          <View style={[styles.iconWrapper, { bottom: '15%', right: '25%' }]}>
            <Ionicons name="hardware-chip" size={20} color={colors.text.inverse} style={styles.icon} />
          </View>
        </View>
        
        {/* Content overlay */}
        <View style={styles.contentOverlay}>
          {children}
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: height * 0.5,
    width: '100%',
  },
  gradient: {
    flex: 1,
    position: 'relative',
  },
  iconContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  iconWrapper: {
    position: 'absolute',
  },
  icon: {
    opacity: 0.3,
  },
  contentOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
});

export default HeroImageFallback;
