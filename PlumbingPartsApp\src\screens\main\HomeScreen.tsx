import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  SafeAreaView,
  Image,
  ImageBackground
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import colors from '../../theme/colors';

const HomeScreen = ({ navigation }) => {
  return (
    <SafeAreaView style={styles.container}>
      <ImageBackground 
        source={require('../../../assets/plumbing-system-illustration.jpg')} 
        style={styles.backgroundImage}
        blurRadius={3}
      >
        <View style={styles.overlay}>
          <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
            <View style={styles.header}>
              <Text style={styles.title}>Plumbing Parts Finder</Text>
              <Text style={styles.subtitle}>Identify, Find, and Order Parts</Text>
            </View>
            
            <View style={styles.featuresContainer}>
              <TouchableOpacity 
                style={styles.featureCard}
                onPress={() => navigation.navigate('Camera')}
              >
                <Ionicons name="camera" size={40} color={colors.primary} />
                <Text style={styles.featureTitle}>Scan Part</Text>
                <Text style={styles.featureDescription}>
                  Take a photo to identify unknown plumbing parts
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.featureCard}
                onPress={() => navigation.navigate('Catalog')}
              >
                <Ionicons name="list" size={40} color={colors.secondary} />
                <Text style={styles.featureTitle}>Browse Catalog</Text>
                <Text style={styles.featureDescription}>
                  Search our extensive catalog of plumbing parts
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.featureCard}
                onPress={() => navigation.navigate('SavedParts')}
              >
                <Ionicons name="bookmark" size={40} color={colors.accent} />
                <Text style={styles.featureTitle}>Saved Parts</Text>
                <Text style={styles.featureDescription}>
                  View your saved parts and previous orders
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={styles.featureCard}
                onPress={() => navigation.navigate('Suppliers')}
              >
                <Ionicons name="business" size={40} color={colors.success} />
                <Text style={styles.featureTitle}>Suppliers</Text>
                <Text style={styles.featureDescription}>
                  Check stock and prices from multiple suppliers
                </Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.recentContainer}>
              <Text style={styles.sectionTitle}>Recently Viewed</Text>
              <ScrollView 
                horizontal 
                showsHorizontalScrollIndicator={false}
                style={styles.recentScroll}
              >
                {[1, 2, 3, 4].map((item) => (
                  <View key={item} style={styles.recentItem}>
                    <View style={styles.recentImagePlaceholder}>
                      <Ionicons name="construct" size={30} color={colors.gray.medium} />
                    </View>
                    <Text style={styles.recentItemName}>Part #{item}</Text>
                    <Text style={styles.recentItemPrice}>${(Math.random() * 50 + 5).toFixed(2)}</Text>
                  </View>
                ))}
              </ScrollView>
            </View>
          </ScrollView>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundImage: {
    flex: 1,
    resizeMode: 'cover',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.85)',
    padding: 16,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    marginTop: 20,
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: colors.textLight,
    marginTop: 8,
    textAlign: 'center',
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  featureCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    width: '48%',
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 12,
    marginBottom: 8,
    color: colors.text,
  },
  featureDescription: {
    fontSize: 12,
    color: colors.textLight,
    textAlign: 'center',
  },
  recentContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: colors.text,
  },
  recentScroll: {
    flexDirection: 'row',
  },
  recentItem: {
    width: 120,
    marginRight: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  recentImagePlaceholder: {
    width: '100%',
    height: 80,
    backgroundColor: colors.gray.light,
    borderRadius: 8,
    marginBottom: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentItemName: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  recentItemPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
    marginTop: 4,
  },
});

export default HomeScreen;

