import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Dimensions,
  StatusBar
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import colors from '../../theme/colors';
import HeroImageFallback from '../../components/HeroImageFallback';

const { width, height } = Dimensions.get('window');

interface HomeScreenProps {
  navigation: {
    navigate: (screen: string) => void;
  };
}

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {

  const renderHeroContent = () => (
    <View style={styles.heroContent}>
      <View style={styles.logoContainer}>
        <Ionicons name="construct-outline" size={60} color={colors.text.inverse} />
      </View>
      <Text style={styles.heroTitle}>Plumbing Parts Finder</Text>
      <Text style={styles.heroSubtitle}>AI-Powered Part Identification & Ordering</Text>

      <TouchableOpacity
        style={styles.ctaButton}
        onPress={() => navigation.navigate('Camera')}
      >
        <LinearGradient
          colors={colors.gradients.primary}
          style={styles.ctaButtonGradient}
        >
          <Ionicons name="camera" size={24} color={colors.text.inverse} />
          <Text style={styles.ctaButtonText}>Start Scanning</Text>
        </LinearGradient>
      </TouchableOpacity>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={colors.background.dark} />

      {/* Hero Section - Using Fallback for Now */}
      <HeroImageFallback style={styles.heroSection}>
        {renderHeroContent()}
      </HeroImageFallback>

      {/* Main Content */}
      <ScrollView style={styles.mainContent} showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.quickActionsContainer}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.surface.primary }]}
              onPress={() => navigation.navigate('Camera')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.primary }]}>
                <Ionicons name="camera" size={28} color={colors.text.inverse} />
              </View>
              <Text style={styles.quickActionTitle}>Scan Part</Text>
              <Text style={styles.quickActionDesc}>Identify with AI</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.surface.primary }]}
              onPress={() => navigation.navigate('History')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.secondary }]}>
                <Ionicons name="search" size={28} color={colors.text.inverse} />
              </View>
              <Text style={styles.quickActionTitle}>Search</Text>
              <Text style={styles.quickActionDesc}>Browse history</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.surface.primary }]}
              onPress={() => navigation.navigate('History')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.accent }]}>
                <Ionicons name="bookmark" size={28} color={colors.text.inverse} />
              </View>
              <Text style={styles.quickActionTitle}>Saved</Text>
              <Text style={styles.quickActionDesc}>Your parts</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.quickActionCard, { backgroundColor: colors.surface.primary }]}
              onPress={() => navigation.navigate('Profile')}
            >
              <View style={[styles.quickActionIcon, { backgroundColor: colors.success }]}>
                <Ionicons name="storefront" size={28} color={colors.text.inverse} />
              </View>
              <Text style={styles.quickActionTitle}>Profile</Text>
              <Text style={styles.quickActionDesc}>Your account</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stats Section */}
        <View style={styles.statsContainer}>
          <LinearGradient
            colors={colors.gradients.warm}
            style={styles.statsGradient}
          >
            <Text style={styles.statsTitle}>Why Choose Our App?</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>50K+</Text>
                <Text style={styles.statLabel}>Parts Identified</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>98%</Text>
                <Text style={styles.statLabel}>Accuracy Rate</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>500+</Text>
                <Text style={styles.statLabel}>Suppliers</Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Featured Parts */}
        <View style={styles.featuredContainer}>
          <Text style={styles.sectionTitle}>Popular Parts</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.featuredScroll}
          >
            {[
              { name: 'Copper Elbow', price: '$12.99', icon: 'git-branch' },
              { name: 'Ball Valve', price: '$24.50', icon: 'radio-button-on' },
              { name: 'PVC Pipe', price: '$8.75', icon: 'remove' },
              { name: 'Faucet Set', price: '$89.99', icon: 'water' },
            ].map((item, index) => (
              <TouchableOpacity key={index} style={styles.featuredItem}>
                <View style={styles.featuredIcon}>
                  <Ionicons name={item.icon as any} size={24} color={colors.primary} />
                </View>
                <Text style={styles.featuredName}>{item.name}</Text>
                <Text style={styles.featuredPrice}>{item.price}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  // Hero Section Styles
  heroSection: {
    height: height * 0.5, // 50% of screen height
    width: '100%',
  },
  heroOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  heroContent: {
    alignItems: 'center',
    maxWidth: width * 0.9,
  },
  logoContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.surface.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    borderWidth: 3,
    borderColor: colors.text.inverse,
  },
  heroTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: colors.shadow.dark,
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  heroSubtitle: {
    fontSize: 16,
    color: colors.text.inverse,
    textAlign: 'center',
    marginBottom: 30,
    opacity: 0.9,
    textShadowColor: colors.shadow.medium,
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },

  // CTA Button Styles
  ctaButton: {
    borderRadius: 25,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: colors.shadow.colored,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  ctaButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 30,
    paddingVertical: 15,
    gap: 10,
  },
  ctaButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text.inverse,
  },

  // Main Content Styles
  mainContent: {
    flex: 1,
    backgroundColor: colors.background.primary,
    paddingHorizontal: 20,
    paddingTop: 20,
  },

  // Quick Actions Styles
  quickActionsContainer: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 20,
    textAlign: 'center',
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 15,
  },
  quickActionCard: {
    width: (width - 55) / 2, // Account for padding and gap
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    elevation: 3,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  quickActionIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  quickActionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text.primary,
    marginBottom: 4,
  },
  quickActionDesc: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Stats Section Styles
  statsContainer: {
    marginBottom: 30,
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 2,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsGradient: {
    padding: 20,
  },
  statsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.text.secondary,
    textAlign: 'center',
  },

  // Featured Parts Styles
  featuredContainer: {
    marginBottom: 30,
  },
  featuredScroll: {
    paddingLeft: 5,
  },
  featuredItem: {
    width: 120,
    marginRight: 15,
    backgroundColor: colors.surface.primary,
    borderRadius: 12,
    padding: 15,
    alignItems: 'center',
    elevation: 2,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  featuredIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.background.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10,
  },
  featuredName: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: 4,
  },
  featuredPrice: {
    fontSize: 14,
    fontWeight: 'bold',
    color: colors.primary,
  },
});

export default HomeScreen;

