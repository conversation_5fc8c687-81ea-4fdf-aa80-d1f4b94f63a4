import type { HeaderBackButtonProps } from '../types';
export declare function HeaderBackButton({ disabled, allowFontScaling, backImage, label, labelStyle, displayMode, onLabelLayout, onPress, pressColor, pressOpacity, screenLayout, tintColor, titleLayout, truncatedLabel, accessibilityLabel, testID, style, href, }: HeaderBackButtonProps): import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=HeaderBackButton.d.ts.map