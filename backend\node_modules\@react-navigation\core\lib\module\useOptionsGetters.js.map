{"version": 3, "names": ["React", "NavigationBuilderContext", "NavigationStateContext", "useOptionsGetters", "key", "options", "navigation", "optionsRef", "useRef", "optionsGettersFromChildRef", "onOptionsChange", "useContext", "addOptionsGetter", "parentAddOptionsGetter", "optionsChangeListener", "useCallback", "isFocused", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "keys", "current", "length", "useEffect", "addListener", "getOptionsFromListener", "result", "getCurrentOptions", "optionsFromListener", "getter"], "sourceRoot": "../../src", "sources": ["useOptionsGetters.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwB,QAAQ,+BAA4B;AACrE,SAASC,sBAAsB,QAAQ,6BAA0B;AASjE,OAAO,SAASC,iBAAiBA,CAAC;EAAEC,GAAG;EAAEC,OAAO;EAAEC;AAAoB,CAAC,EAAE;EACvE,MAAMC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAAqBH,OAAO,CAAC;EAC5D,MAAMI,0BAA0B,GAAGT,KAAK,CAACQ,MAAM,CAE7C,CAAC,CAAC,CAAC;EAEL,MAAM;IAAEE;EAAgB,CAAC,GAAGV,KAAK,CAACW,UAAU,CAACV,wBAAwB,CAAC;EACtE,MAAM;IAAEW,gBAAgB,EAAEC;EAAuB,CAAC,GAAGb,KAAK,CAACW,UAAU,CACnET,sBACF,CAAC;EAED,MAAMY,qBAAqB,GAAGd,KAAK,CAACe,WAAW,CAAC,MAAM;IACpD,MAAMC,SAAS,GAAGV,UAAU,EAAEU,SAAS,CAAC,CAAC,IAAI,IAAI;IACjD,MAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACV,0BAA0B,CAACW,OAAO,CAAC,CAACC,MAAM;IAE1E,IAAIL,SAAS,IAAI,CAACC,WAAW,EAAE;MAC7BP,eAAe,CAACH,UAAU,CAACa,OAAO,IAAI,CAAC,CAAC,CAAC;IAC3C;EACF,CAAC,EAAE,CAACd,UAAU,EAAEI,eAAe,CAAC,CAAC;EAEjCV,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpBf,UAAU,CAACa,OAAO,GAAGf,OAAO;IAC5BS,qBAAqB,CAAC,CAAC;IAEvB,OAAOR,UAAU,EAAEiB,WAAW,CAAC,OAAO,EAAET,qBAAqB,CAAC;EAChE,CAAC,EAAE,CAACR,UAAU,EAAED,OAAO,EAAES,qBAAqB,CAAC,CAAC;EAEhD,MAAMU,sBAAsB,GAAGxB,KAAK,CAACe,WAAW,CAAC,MAAM;IACrD,KAAK,MAAMX,GAAG,IAAIK,0BAA0B,CAACW,OAAO,EAAE;MACpD,IAAIhB,GAAG,IAAIK,0BAA0B,CAACW,OAAO,EAAE;QAC7C,MAAMK,MAAM,GAAGhB,0BAA0B,CAACW,OAAO,CAAChB,GAAG,CAAC,GAAG,CAAC;;QAE1D;QACA,IAAIqB,MAAM,KAAK,IAAI,EAAE;UACnB,OAAOA,MAAM;QACf;MACF;IACF;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,iBAAiB,GAAG1B,KAAK,CAACe,WAAW,CAAC,MAAM;IAChD,MAAMC,SAAS,GAAGV,UAAU,EAAEU,SAAS,CAAC,CAAC,IAAI,IAAI;IAEjD,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,IAAI;IACb;IAEA,MAAMW,mBAAmB,GAAGH,sBAAsB,CAAC,CAAC;IAEpD,IAAIG,mBAAmB,KAAK,IAAI,EAAE;MAChC,OAAOA,mBAAmB;IAC5B;IAEA,OAAOpB,UAAU,CAACa,OAAO;EAC3B,CAAC,EAAE,CAACd,UAAU,EAAEkB,sBAAsB,CAAC,CAAC;EAExCxB,KAAK,CAACsB,SAAS,CAAC,MAAM;IACpB,OAAOT,sBAAsB,GAAGT,GAAG,EAAGsB,iBAAiB,CAAC;EAC1D,CAAC,EAAE,CAACA,iBAAiB,EAAEb,sBAAsB,EAAET,GAAG,CAAC,CAAC;EAEpD,MAAMQ,gBAAgB,GAAGZ,KAAK,CAACe,WAAW,CACxC,CAACX,GAAW,EAAEwB,MAAuC,KAAK;IACxDnB,0BAA0B,CAACW,OAAO,CAAChB,GAAG,CAAC,GAAGwB,MAAM;IAChDd,qBAAqB,CAAC,CAAC;IAEvB,OAAO,MAAM;MACX;MACA,OAAOL,0BAA0B,CAACW,OAAO,CAAChB,GAAG,CAAC;MAC9CU,qBAAqB,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EACD,CAACA,qBAAqB,CACxB,CAAC;EAED,OAAO;IACLF,gBAAgB;IAChBc;EACF,CAAC;AACH", "ignoreList": []}