{"version": 3, "names": ["React", "ThemeContext", "useTheme", "theme", "useContext", "Error"], "sourceRoot": "../../../src", "sources": ["theming/useTheme.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,YAAY,QAAQ,mBAAgB;AAE7C,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,YAAY,CAAC;EAE5C,IAAIE,KAAK,IAAI,IAAI,EAAE;IACjB,MAAM,IAAIE,KAAK,CACb,8FACF,CAAC;EACH;EAEA,OAAOF,KAAK;AACd", "ignoreList": []}