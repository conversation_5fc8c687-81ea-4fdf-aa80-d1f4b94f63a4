{"version": 3, "names": ["React", "Reanimated", "tagMessage", "Wrap", "Component", "render", "child", "Children", "only", "props", "children", "cloneElement", "collapsable", "e", "Error", "AnimatedWrap", "default", "createAnimatedComponent"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/Wrap.tsx"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,sBAAsB;AACjD,SAASC,UAAU,QAAQ,gBAAgB;AAE3C,OAAO,MAAMC,IAAI,SAASH,KAAK,CAACI,SAAS,CAItC;EACDC,MAAMA,CAAA,EAAG;IACP,IAAI;MACF;MACA;MACA;MACA;MACA;MACA,MAAMC,KAAU,GAAGN,KAAK,CAACO,QAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC;MAC3D,oBAAOV,KAAK,CAACW,YAAY,CACvBL,KAAK,EACL;QAAEM,WAAW,EAAE;MAAM,CAAC;MACtB;MACAN,KAAK,CAACG,KAAK,CAACC,QACd,CAAC;IACH,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV,MAAM,IAAIC,KAAK,CACbZ,UAAU,CACR,2KACF,CACF,CAAC;IACH;EACF;AACF;AAEA,OAAO,MAAMa,YAAY,GACvBd,UAAU,EAAEe,OAAO,EAAEC,uBAAuB,CAACd,IAAI,CAAC,IAAIA,IAAI", "ignoreList": []}