{"version": 3, "file": "BottomTabItem.d.ts", "sourceRoot": "", "sources": ["../../../../src/views/BottomTabItem.tsx"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,KAAK,EAAY,MAAM,0BAA0B,CAAC;AAEhE,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EACL,KAAK,qBAAqB,EAE1B,KAAK,SAAS,EAEd,KAAK,SAAS,EAEd,KAAK,SAAS,EACf,MAAM,cAAc,CAAC;AAEtB,OAAO,KAAK,EACV,uBAAuB,EACvB,mBAAmB,EACnB,aAAa,EACd,MAAM,UAAU,CAAC;AAGlB,KAAK,KAAK,GAAG;IACX;;OAEG;IACH,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IACrB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,UAAU,EAAE,mBAAmB,CAAC;IAChC;;OAEG;IACH,KAAK,EACD,MAAM,GACN,CAAC,CAAC,KAAK,EAAE;QACP,OAAO,EAAE,OAAO,CAAC;QACjB,KAAK,EAAE,MAAM,CAAC;QACd,QAAQ,EAAE,aAAa,CAAC;QACxB,QAAQ,EAAE,MAAM,CAAC;KAClB,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC;IAC3B;;OAEG;IACH,IAAI,EAAE,CAAC,KAAK,EAAE;QACZ,OAAO,EAAE,OAAO,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,KAAK,EAAE,MAAM,CAAC;KACf,KAAK,KAAK,CAAC,SAAS,CAAC;IACtB;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IACxB;;OAEG;IACH,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAClC;;OAEG;IACH,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,uBAAuB,KAAK,KAAK,CAAC,SAAS,CAAC;IAC7D;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB;;;OAGG;IACH,OAAO,EAAE,CACP,CAAC,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG,qBAAqB,KACjE,IAAI,CAAC;IACV;;OAEG;IACH,WAAW,EAAE,CAAC,CAAC,EAAE,qBAAqB,KAAK,IAAI,CAAC;IAChD;;OAEG;IACH,UAAU,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB;;;;OAIG;IACH,OAAO,EAAE,OAAO,GAAG,UAAU,CAAC;IAC9B;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,qBAAqB,CAAC,EAAE,MAAM,CAAC;IAC/B;;OAEG;IACH,uBAAuB,CAAC,EAAE,MAAM,CAAC;IACjC;;OAEG;IACH,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB;;;OAGG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B;;OAEG;IACH,UAAU,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAClC;;OAEG;IACH,SAAS,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IACjC;;OAEG;IACH,KAAK,CAAC,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;CAC9B,CAAC;AASF,wBAAgB,aAAa,CAAC,EAC5B,KAAK,EACL,IAAI,EACJ,OAAO,EACP,UAAU,EACV,KAAK,EACL,IAAI,EACJ,KAAK,EACL,UAAU,EACV,MAA4B,EAC5B,kBAAkB,EAClB,MAAM,EACN,OAAO,EACP,WAAW,EACX,UAAU,EACV,OAAO,EACP,OAAO,EACP,OAAO,EACP,eAAe,EAAE,qBAAqB,EACtC,iBAAiB,EAAE,uBAAuB,EAC1C,qBAAqB,EAAE,2BAA2B,EAClD,uBAAuC,EACvC,SAAgB,EAIhB,gBAAoE,EACpE,UAAU,EACV,SAAS,EACT,KAAK,GACN,EAAE,KAAK,2CAgMP"}