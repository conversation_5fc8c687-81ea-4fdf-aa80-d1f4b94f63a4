{"version": 3, "names": ["registerHandler", "RNGestureHandlerModule", "filterConfig", "scheduleFlushOperations", "ghQueueMicrotask", "extractGestureRelations", "checkGestureCallbacksForWorklets", "ALLOWED_PROPS", "updateHandlers", "preparedGesture", "gestureConfig", "newGestures", "prepare", "i", "length", "handler", "attachedGestures", "handlerTag", "handlers", "isMounted", "shouldUpdateSharedValueIfUsed", "gestureId", "shouldUseReanimated", "config", "updateGestureHandler", "testId", "animatedHandlers", "newHandlersValue", "filter", "g", "map", "value"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/updateHandlers.ts"], "mappings": ";;AACA,SAASA,eAAe,QAAQ,wBAAwB;AACxD,OAAOC,sBAAsB,MAAM,iCAAiC;AACpE,SAASC,YAAY,EAAEC,uBAAuB,QAAQ,aAAa;AAEnE,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SACEC,uBAAuB,EACvBC,gCAAgC,EAChCC,aAAa,QACR,SAAS;AAEhB,OAAO,SAASC,cAAcA,CAC5BC,eAAqC,EACrCC,aAA4C,EAC5CC,WAA0B,EAC1B;EACAD,aAAa,CAACE,OAAO,CAAC,CAAC;EAEvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IAC3C,MAAME,OAAO,GAAGN,eAAe,CAACO,gBAAgB,CAACH,CAAC,CAAC;IACnDP,gCAAgC,CAACS,OAAO,CAAC;;IAEzC;IACA;IACA,IAAIJ,WAAW,CAACE,CAAC,CAAC,CAACI,UAAU,KAAKF,OAAO,CAACE,UAAU,EAAE;MACpDN,WAAW,CAACE,CAAC,CAAC,CAACI,UAAU,GAAGF,OAAO,CAACE,UAAU;MAC9CN,WAAW,CAACE,CAAC,CAAC,CAACK,QAAQ,CAACD,UAAU,GAAGF,OAAO,CAACE,UAAU;IACzD;EACF;;EAEA;EACA,MAAMD,gBAAgB,GAAGP,eAAe,CAACO,gBAAgB;;EAEzD;EACA;EACA;EACAZ,gBAAgB,CAAC,MAAM;IACrB,IAAI,CAACK,eAAe,CAACU,SAAS,EAAE;MAC9B;IACF;;IAEA;IACA,IAAIH,gBAAgB,KAAKP,eAAe,CAACO,gBAAgB,EAAE;MACzD;IACF;;IAEA;IACA,IAAII,6BAA6B,GAC/BJ,gBAAgB,CAACF,MAAM,KAAKH,WAAW,CAACG,MAAM;IAEhD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,WAAW,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC3C,MAAME,OAAO,GAAGC,gBAAgB,CAACH,CAAC,CAAC;;MAEnC;MACA;MACA,IACEE,OAAO,CAACG,QAAQ,CAACG,SAAS,KAAKV,WAAW,CAACE,CAAC,CAAC,CAACK,QAAQ,CAACG,SAAS,KAC/DV,WAAW,CAACE,CAAC,CAAC,CAACS,mBAAmB,IAAIP,OAAO,CAACO,mBAAmB,CAAC,EACnE;QACAF,6BAA6B,GAAG,IAAI;MACtC;MAEAL,OAAO,CAACQ,MAAM,GAAGZ,WAAW,CAACE,CAAC,CAAC,CAACU,MAAM;MACtCR,OAAO,CAACG,QAAQ,GAAGP,WAAW,CAACE,CAAC,CAAC,CAACK,QAAQ;MAE1CjB,sBAAsB,CAACuB,oBAAoB,CACzCT,OAAO,CAACE,UAAU,EAClBf,YAAY,CACVa,OAAO,CAACQ,MAAM,EACdhB,aAAa,EACbF,uBAAuB,CAACU,OAAO,CACjC,CACF,CAAC;MAEDf,eAAe,CAACe,OAAO,CAACE,UAAU,EAAEF,OAAO,EAAEA,OAAO,CAACQ,MAAM,CAACE,MAAM,CAAC;IACrE;IAEA,IAAIhB,eAAe,CAACiB,gBAAgB,IAAIN,6BAA6B,EAAE;MACrE,MAAMO,gBAAgB,GAAGX,gBAAgB,CACtCY,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACP,mBAAmB,CAAC,CAAC;MAAA,CACrCQ,GAAG,CAAED,CAAC,IAAKA,CAAC,CAACX,QAAQ,CAErB;MAEHT,eAAe,CAACiB,gBAAgB,CAACK,KAAK,GAAGJ,gBAAgB;IAC3D;IAEAxB,uBAAuB,CAAC,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}