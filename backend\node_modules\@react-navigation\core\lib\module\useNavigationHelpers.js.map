{"version": 3, "names": ["CommonActions", "React", "NavigationContext", "PrivateValueStore", "UnhandledActionContext", "useNavigationHelpers", "id", "navigatorId", "onAction", "getState", "emitter", "router", "stateRef", "onUnhandledAction", "useContext", "parentNavigationHelpers", "useMemo", "dispatch", "op", "action", "handled", "actions", "actionCreators", "helpers", "Object", "keys", "reduce", "acc", "name", "args", "navigationHelpers", "emit", "isFocused", "canGoBack", "state", "getStateForAction", "goBack", "routeNames", "routeParamList", "routeGetIdList", "getId", "getParent", "undefined", "current"], "sourceRoot": "../../src", "sources": ["useNavigationHelpers.tsx"], "mappings": ";;AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiB,QAAQ,wBAAqB;AACvD,SAAiCC,iBAAiB,QAAQ,YAAS;AACnE,SAASC,sBAAsB,QAAQ,6BAA0B;AAGjE;AACA;AACAD,iBAAiB;AAWjB;AACA;AACA;AACA;AACA,OAAO,SAASE,oBAAoBA,CAKlC;EACAC,EAAE,EAAEC,WAAW;EACfC,QAAQ;EACRC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACNC;AACsB,CAAC,EAAE;EACzB,MAAMC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACV,sBAAsB,CAAC;EAClE,MAAMW,uBAAuB,GAAGd,KAAK,CAACa,UAAU,CAACZ,iBAAiB,CAAC;EAEnE,OAAOD,KAAK,CAACe,OAAO,CAAC,MAAM;IACzB,MAAMC,QAAQ,GAAIC,EAAuC,IAAK;MAC5D,MAAMC,MAAM,GAAG,OAAOD,EAAE,KAAK,UAAU,GAAGA,EAAE,CAACT,QAAQ,CAAC,CAAC,CAAC,GAAGS,EAAE;MAE7D,MAAME,OAAO,GAAGZ,QAAQ,CAACW,MAAM,CAAC;MAEhC,IAAI,CAACC,OAAO,EAAE;QACZP,iBAAiB,GAAGM,MAAM,CAAC;MAC7B;IACF,CAAC;IAED,MAAME,OAAO,GAAG;MACd,GAAGV,MAAM,CAACW,cAAc;MACxB,GAAGtB;IACL,CAAC;IAED,MAAMuB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACJ,OAAO,CAAC,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACzD;MACAD,GAAG,CAACC,IAAI,CAAC,GAAG,CAAC,GAAGC,IAAS,KAAKZ,QAAQ,CAACI,OAAO,CAACO,IAAI,CAAC,CAAC,GAAGC,IAAI,CAAC,CAAC;MAC9D,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAkB,CAAC;IAEvB,MAAMG,iBAAiB,GAAG;MACxB,GAAGf,uBAAuB;MAC1B,GAAGQ,OAAO;MACVN,QAAQ;MACRc,IAAI,EAAErB,OAAO,CAACqB,IAAI;MAClBC,SAAS,EAAEjB,uBAAuB,GAC9BA,uBAAuB,CAACiB,SAAS,GACjC,MAAM,IAAI;MACdC,SAAS,EAAEA,CAAA,KAAM;QACf,MAAMC,KAAK,GAAGzB,QAAQ,CAAC,CAAC;QAExB,OACEE,MAAM,CAACwB,iBAAiB,CAACD,KAAK,EAAElC,aAAa,CAACoC,MAAM,CAAC,CAAC,EAAY;UAChEC,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BC,cAAc,EAAE,CAAC,CAAC;UAClBC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,KAAK,IAAI,IACXxB,uBAAuB,EAAEkB,SAAS,CAAC,CAAC,IACpC,KAAK;MAET,CAAC;MACDO,KAAK,EAAEA,CAAA,KAAMjC,WAAW;MACxBkC,SAAS,EAAGnC,EAAW,IAAK;QAC1B,IAAIA,EAAE,KAAKoC,SAAS,EAAE;UACpB,IAAIC,OAAO,GAAGb,iBAAiB;UAE/B,OAAOa,OAAO,IAAIrC,EAAE,KAAKqC,OAAO,CAACH,KAAK,CAAC,CAAC,EAAE;YACxCG,OAAO,GAAGA,OAAO,CAACF,SAAS,CAAC,CAAC;UAC/B;UAEA,OAAOE,OAAO;QAChB;QAEA,OAAO5B,uBAAuB;MAChC,CAAC;MACDN,QAAQ,EAAEA,CAAA,KAAa;QACrB;QACA;QACA;QACA;QACA;QACA,IAAIG,QAAQ,CAAC+B,OAAO,IAAI,IAAI,EAAE;UAC5B,OAAO/B,QAAQ,CAAC+B,OAAO;QACzB;QAEA,OAAOlC,QAAQ,CAAC,CAAC;MACnB;IACF,CAA+D;IAE/D,OAAOqB,iBAAiB;EAC1B,CAAC,EAAE,CACDnB,MAAM,EACNI,uBAAuB,EACvBL,OAAO,CAACqB,IAAI,EACZtB,QAAQ,EACRD,QAAQ,EACRK,iBAAiB,EACjBN,WAAW,EACXK,QAAQ,CACT,CAAC;AACJ", "ignoreList": []}