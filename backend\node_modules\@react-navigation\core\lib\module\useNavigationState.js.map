{"version": 3, "names": ["React", "useLatestCallback", "useSyncExternalStoreWithSelector", "useClientLayoutEffect", "jsx", "_jsx", "useNavigationState", "selector", "stateListener", "useContext", "NavigationStateListenerContext", "Error", "value", "subscribe", "getState", "NavigationStateListenerProvider", "state", "children", "listeners", "useRef", "callback", "current", "push", "filter", "cb", "for<PERSON>ach", "context", "useMemo", "Provider", "createContext", "undefined"], "sourceRoot": "../../src", "sources": ["useNavigationState.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,gCAAgC,QAAQ,uCAAuC;AAExF,SAASC,qBAAqB,QAAQ,4BAAyB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAMhE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAChCC,QAAgC,EAC7B;EACH,MAAMC,aAAa,GAAGR,KAAK,CAACS,UAAU,CAACC,8BAA8B,CAAC;EAEtE,IAAIF,aAAa,IAAI,IAAI,EAAE;IACzB,MAAM,IAAIG,KAAK,CACb,0EACF,CAAC;EACH;EAEA,MAAMC,KAAK,GAAGV,gCAAgC,CAC5CM,aAAa,CAACK,SAAS;EACvB;EACAL,aAAa,CAACM,QAAQ,EACtBN,aAAa,CAACM,QAAQ,EACtBP,QACF,CAAC;EAED,OAAOK,KAAK;AACd;AAEA,OAAO,SAASG,+BAA+BA,CAAC;EAC9CC,KAAK;EACLC;AAIF,CAAC,EAAE;EACD,MAAMC,SAAS,GAAGlB,KAAK,CAACmB,MAAM,CAAiB,EAAE,CAAC;EAElD,MAAML,QAAQ,GAAGb,iBAAiB,CAAC,MAAMe,KAAK,CAAC;EAE/C,MAAMH,SAAS,GAAGZ,iBAAiB,CAAEmB,QAAoB,IAAK;IAC5DF,SAAS,CAACG,OAAO,CAACC,IAAI,CAACF,QAAQ,CAAC;IAEhC,OAAO,MAAM;MACXF,SAAS,CAACG,OAAO,GAAGH,SAAS,CAACG,OAAO,CAACE,MAAM,CAAEC,EAAE,IAAKA,EAAE,KAAKJ,QAAQ,CAAC;IACvE,CAAC;EACH,CAAC,CAAC;EAEFjB,qBAAqB,CAAC,MAAM;IAC1Be,SAAS,CAACG,OAAO,CAACI,OAAO,CAAEL,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;EACrD,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;EAEX,MAAMU,OAAO,GAAG1B,KAAK,CAAC2B,OAAO,CAC3B,OAAO;IACLb,QAAQ;IACRD;EACF,CAAC,CAAC,EACF,CAACC,QAAQ,EAAED,SAAS,CACtB,CAAC;EAED,oBACER,IAAA,CAACK,8BAA8B,CAACkB,QAAQ;IAAChB,KAAK,EAAEc,OAAQ;IAAAT,QAAA,EACrDA;EAAQ,CAC8B,CAAC;AAE9C;AAEA,MAAMP,8BAA8B,gBAAGV,KAAK,CAAC6B,aAAa,CAMxDC,SAAS,CAAC", "ignoreList": []}