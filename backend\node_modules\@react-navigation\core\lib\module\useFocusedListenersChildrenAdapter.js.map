{"version": 3, "names": ["React", "NavigationBuilderContext", "useFocusedListenersChildrenAdapter", "navigation", "focusedListeners", "addListener", "useContext", "listener", "useCallback", "callback", "isFocused", "handled", "result", "useEffect"], "sourceRoot": "../../src", "sources": ["useFocusedListenersChildrenAdapter.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAGEC,wBAAwB,QACnB,+BAA4B;AAQnC;AACA;AACA;AACA,OAAO,SAASC,kCAAkCA,CAAC;EACjDC,UAAU;EACVC;AACO,CAAC,EAAE;EACV,MAAM;IAAEC;EAAY,CAAC,GAAGL,KAAK,CAACM,UAAU,CAACL,wBAAwB,CAAC;EAElE,MAAMM,QAAQ,GAAGP,KAAK,CAACQ,WAAW,CAC/BC,QAAwC,IAAK;IAC5C,IAAIN,UAAU,CAACO,SAAS,CAAC,CAAC,EAAE;MAC1B,KAAK,MAAMH,QAAQ,IAAIH,gBAAgB,EAAE;QACvC,MAAM;UAAEO,OAAO;UAAEC;QAAO,CAAC,GAAGL,QAAQ,CAACE,QAAQ,CAAC;QAE9C,IAAIE,OAAO,EAAE;UACX,OAAO;YAAEA,OAAO;YAAEC;UAAO,CAAC;QAC5B;MACF;MAEA,OAAO;QAAED,OAAO,EAAE,IAAI;QAAEC,MAAM,EAAEH,QAAQ,CAACN,UAAU;MAAE,CAAC;IACxD,CAAC,MAAM;MACL,OAAO;QAAEQ,OAAO,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAK,CAAC;IACzC;EACF,CAAC,EACD,CAACR,gBAAgB,EAAED,UAAU,CAC/B,CAAC;EAEDH,KAAK,CAACa,SAAS,CACb,MAAMR,WAAW,GAAG,OAAO,EAAEE,QAAQ,CAAC,EACtC,CAACF,WAAW,EAAEE,QAAQ,CACxB,CAAC;AACH", "ignoreList": []}