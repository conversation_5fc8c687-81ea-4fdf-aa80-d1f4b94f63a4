{"version": 3, "names": ["React", "View", "Fragment", "_Fragment", "jsx", "_jsx", "Dummy", "children", "PanGestureHandler", "GestureHandlerRootView", "GestureState", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;AAAC,SAAAC,QAAA,IAAAC,SAAA,EAAAC,GAAA,IAAAC,IAAA;AAGpC,MAAMC,KAAU,GAAGA,CAAC;EAAEC;AAAwC,CAAC,kBAC7DF,IAAA,CAAAF,SAAA;EAAAI,QAAA,EAAGA;AAAQ,CAAG,CACf;AAED,OAAO,MAAMC,iBAAiB,GAC5BF,KAAyD;AAE3D,OAAO,MAAMG,sBAAsB,GAAGR,IAAI;AAE1C,OAAO,MAAMS,YAAY,GAAG;EAC1BC,YAAY,EAAE,CAAC;EACfC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,SAAS,EAAE,CAAC;EACZC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE;AACP,CAAC", "ignoreList": []}