{"version": 3, "names": ["React", "useClientLayoutEffect", "document", "navigator", "product", "useLayoutEffect", "useEffect"], "sourceRoot": "../../src", "sources": ["useClientLayoutEffect.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAChC,OAAOC,QAAQ,KAAK,WAAW,IAC9B,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,OAAO,KAAK,aAAc,GACrEJ,KAAK,CAACK,eAAe,GACrBL,KAAK,CAACM,SAAS", "ignoreList": []}