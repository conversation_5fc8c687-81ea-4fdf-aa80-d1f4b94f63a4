{"version": 3, "names": ["React", "GestureHandlerRefContext", "useGestureHandlerRef", "ref", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useGestureHandlerRef.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwB,QAAQ,+BAA4B;AAErE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,MAAMC,GAAG,GAAGH,KAAK,CAACI,UAAU,CAACH,wBAAwB,CAAC;EAEtD,IAAIE,GAAG,KAAKE,SAAS,EAAE;IACrB,MAAM,IAAIC,KAAK,CACb,4EACF,CAAC;EACH;EAEA,OAAOH,GAAG;AACZ", "ignoreList": []}