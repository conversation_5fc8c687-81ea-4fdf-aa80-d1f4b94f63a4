{"version": 3, "file": "useNavigationState.d.ts", "sourceRoot": "", "sources": ["../../../src/useNavigationState.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,eAAe,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAC;AAChF,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAM/B,KAAK,QAAQ,CAAC,SAAS,SAAS,aAAa,EAAE,CAAC,IAAI,CAClD,KAAK,EAAE,eAAe,CAAC,SAAS,CAAC,KAC9B,CAAC,CAAC;AAEP;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,SAAS,SAAS,aAAa,EAAE,CAAC,EACnE,QAAQ,EAAE,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,GAC/B,CAAC,CAkBH;AAED,wBAAgB,+BAA+B,CAAC,EAC9C,KAAK,EACL,QAAQ,GACT,EAAE;IACD,KAAK,EAAE,eAAe,CAAC,aAAa,CAAC,CAAC;IACtC,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC;CAC3B,2CA8BA"}