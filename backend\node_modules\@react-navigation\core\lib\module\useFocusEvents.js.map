{"version": 3, "names": ["React", "NavigationContext", "useFocusEvents", "state", "emitter", "navigation", "useContext", "lastFocusedKeyRef", "useRef", "undefined", "currentFocusedKey", "routes", "index", "key", "useEffect", "addListener", "current", "emit", "type", "target", "lastFocused<PERSON>ey", "isFocused"], "sourceRoot": "../../src", "sources": ["useFocusEvents.tsx"], "mappings": ";;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,iBAAiB,QAAQ,wBAAqB;AASvD;AACA;AACA;AACA,OAAO,SAASC,cAAcA,CAAgC;EAC5DC,KAAK;EACLC;AACc,CAAC,EAAE;EACjB,MAAMC,UAAU,GAAGL,KAAK,CAACM,UAAU,CAACL,iBAAiB,CAAC;EACtD,MAAMM,iBAAiB,GAAGP,KAAK,CAACQ,MAAM,CAAqBC,SAAS,CAAC;EAErE,MAAMC,iBAAiB,GAAGP,KAAK,CAACQ,MAAM,CAACR,KAAK,CAACS,KAAK,CAAC,CAACC,GAAG;;EAEvD;EACA;EACAb,KAAK,CAACc,SAAS,CACb,MACET,UAAU,EAAEU,WAAW,CAAC,OAAO,EAAE,MAAM;IACrCR,iBAAiB,CAACS,OAAO,GAAGN,iBAAiB;IAC7CN,OAAO,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEN,OAAO,EAAEC,UAAU,CACzC,CAAC;EAEDL,KAAK,CAACc,SAAS,CACb,MACET,UAAU,EAAEU,WAAW,CAAC,MAAM,EAAE,MAAM;IACpCR,iBAAiB,CAACS,OAAO,GAAGP,SAAS;IACrCL,OAAO,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC3D,CAAC,CAAC,EACJ,CAACA,iBAAiB,EAAEN,OAAO,EAAEC,UAAU,CACzC,CAAC;EAEDL,KAAK,CAACc,SAAS,CAAC,MAAM;IACpB,MAAMM,cAAc,GAAGb,iBAAiB,CAACS,OAAO;IAEhDT,iBAAiB,CAACS,OAAO,GAAGN,iBAAiB;;IAE7C;IACA;IACA,IAAIU,cAAc,KAAKX,SAAS,IAAI,CAACJ,UAAU,EAAE;MAC/CD,OAAO,CAACa,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC5D;;IAEA;IACA;IACA,IACEU,cAAc,KAAKV,iBAAiB,IACpC,EAAEL,UAAU,GAAGA,UAAU,CAACgB,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,EAC7C;MACA;IACF;IAEA,IAAID,cAAc,KAAKX,SAAS,EAAE;MAChC;MACA;IACF;IAEAL,OAAO,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAEC;IAAe,CAAC,CAAC;IACtDhB,OAAO,CAACa,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,iBAAiB,EAAEN,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC9C", "ignoreList": []}