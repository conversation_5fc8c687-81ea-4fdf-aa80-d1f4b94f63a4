{"version": 3, "names": ["React", "NavigationBuilderContext", "useOnRouteFocus", "router", "getState", "key", "sourceRouteKey", "setState", "onRouteFocus", "onRouteFocusParent", "useContext", "useCallback", "state", "result", "getStateForRouteFocus", "undefined"], "sourceRoot": "../../src", "sources": ["useOnRouteFocus.tsx"], "mappings": ";;AAKA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwB,QAAQ,+BAA4B;AASrE;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAkC;EAC/DC,MAAM;EACNC,QAAQ;EACRC,GAAG,EAAEC,cAAc;EACnBC;AACe,CAAC,EAAE;EAClB,MAAM;IAAEC,YAAY,EAAEC;EAAmB,CAAC,GAAGT,KAAK,CAACU,UAAU,CAC3DT,wBACF,CAAC;EAED,OAAOD,KAAK,CAACW,WAAW,CACrBN,GAAW,IAAK;IACf,MAAMO,KAAK,GAAGR,QAAQ,CAAC,CAAC;IACxB,MAAMS,MAAM,GAAGV,MAAM,CAACW,qBAAqB,CAACF,KAAK,EAAEP,GAAG,CAAC;IAEvD,IAAIQ,MAAM,KAAKD,KAAK,EAAE;MACpBL,QAAQ,CAACM,MAAM,CAAC;IAClB;IAEA,IAAIJ,kBAAkB,KAAKM,SAAS,IAAIT,cAAc,KAAKS,SAAS,EAAE;MACpEN,kBAAkB,CAACH,cAAc,CAAC;IACpC;EACF,CAAC,EACD,CAACF,QAAQ,EAAEK,kBAAkB,EAAEN,MAAM,EAAEI,QAAQ,EAAED,cAAc,CACjE,CAAC;AACH", "ignoreList": []}