{"version": 3, "names": ["getDefaultHeaderHeight", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useLocale", "React", "Platform", "StyleSheet", "memoize", "jsx", "_jsx", "HeaderSegment", "props", "direction", "leftLabelLayout", "setLeftLabelLayout", "useState", "undefined", "titleLayout", "setTitleLayout", "handleTitleLayout", "e", "height", "width", "nativeEvent", "layout", "handleLeftLabelLayout", "getInterpolatedStyle", "styleInterpolator", "current", "next", "headerHeight", "progress", "layouts", "header", "screen", "title", "leftLabel", "modal", "onGoBack", "backHref", "headerTitle", "headerLeft", "left", "headerRight", "right", "headerBackImage", "headerBackTitle", "headerBackButtonDisplayMode", "OS", "headerBackTruncatedTitle", "headerBackAccessibilityLabel", "headerBackTestID", "headerBackAllowFontScaling", "headerBackTitleStyle", "headerTitleContainerStyle", "headerLeftContainerStyle", "headerRightContainerStyle", "headerBackgroundContainerStyle", "headerStyle", "customHeaderStyle", "headerStatusBarHeight", "rest", "defaultHeight", "flatten", "titleStyle", "leftButtonStyle", "leftLabelStyle", "rightButtonStyle", "backgroundStyle", "href", "backImage", "accessibilityLabel", "testID", "allowFontScaling", "onPress", "label", "truncatedLabel", "labelStyle", "onLabelLayout", "screenLayout", "canGoBack", "Boolean", "onLayout"], "sourceRoot": "../../../../src", "sources": ["views/Header/HeaderSegment.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EACtBC,MAAM,EACNC,gBAAgB,EAEhBC,WAAW,QACN,4BAA4B;AACnC,SAASC,SAAS,QAAQ,0BAA0B;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAGEC,QAAQ,EACRC,UAAU,QAEL,cAAc;AAQrB,SAASC,OAAO,QAAQ,wBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAa9C,OAAO,SAASC,aAAaA,CAACC,KAAY,EAAE;EAC1C,MAAM;IAAEC;EAAU,CAAC,GAAGT,SAAS,CAAC,CAAC;EAEjC,MAAM,CAACU,eAAe,EAAEC,kBAAkB,CAAC,GAAGV,KAAK,CAACW,QAAQ,CAE1DC,SAAS,CAAC;EAEZ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,KAAK,CAACW,QAAQ,CAClDC,SACF,CAAC;EAED,MAAMG,iBAAiB,GAAIC,CAAoB,IAAK;IAClD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9CN,cAAc,CAAED,WAAW,IAAK;MAC9B,IACEA,WAAW,IACXI,MAAM,KAAKJ,WAAW,CAACI,MAAM,IAC7BC,KAAK,KAAKL,WAAW,CAACK,KAAK,EAC3B;QACA,OAAOL,WAAW;MACpB;MAEA,OAAO;QAAEI,MAAM;QAAEC;MAAM,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,qBAAqB,GAAIL,CAAoB,IAAK;IACtD,MAAM;MAAEC,MAAM;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,WAAW,CAACC,MAAM;IAE9C,IACEX,eAAe,IACfQ,MAAM,KAAKR,eAAe,CAACQ,MAAM,IACjCC,KAAK,KAAKT,eAAe,CAACS,KAAK,EAC/B;MACA;IACF;IAEAR,kBAAkB,CAAC;MAAEO,MAAM;MAAEC;IAAM,CAAC,CAAC;EACvC,CAAC;EAED,MAAMI,oBAAoB,GAAGnB,OAAO,CAClC,CACEoB,iBAA+C,EAC/CH,MAAc,EACdI,OAA+C,EAC/CC,IAAwD,EACxDZ,WAA+B,EAC/BJ,eAAmC,EACnCiB,YAAoB,KAEpBH,iBAAiB,CAAC;IAChBC,OAAO,EAAE;MAAEG,QAAQ,EAAEH;IAAQ,CAAC;IAC9BC,IAAI,EAAEA,IAAI,IAAI;MAAEE,QAAQ,EAAEF;IAAK,CAAC;IAChCjB,SAAS;IACToB,OAAO,EAAE;MACPC,MAAM,EAAE;QACNZ,MAAM,EAAES,YAAY;QACpBR,KAAK,EAAEE,MAAM,CAACF;MAChB,CAAC;MACDY,MAAM,EAAEV,MAAM;MACdW,KAAK,EAAElB,WAAW;MAClBmB,SAAS,EAAEvB;IACb;EACF,CAAC,CACL,CAAC;EAED,MAAM;IACJkB,QAAQ;IACRP,MAAM;IACNa,KAAK;IACLC,QAAQ;IACRC,QAAQ;IACRC,WAAW,EAAEL,KAAK;IAClBM,UAAU,EAAEC,IAAI,GAAGJ,QAAQ,GACtB3B,KAA4B,iBAAKF,IAAA,CAACR,gBAAgB;MAAA,GAAKU;IAAK,CAAG,CAAC,GACjEK,SAAS;IACb2B,WAAW,EAAEC,KAAK;IAClBC,eAAe;IACfC,eAAe;IACfC,2BAA2B,GAAG1C,QAAQ,CAAC2C,EAAE,KAAK,KAAK,GAAG,SAAS,GAAG,SAAS;IAC3EC,wBAAwB;IACxBC,4BAA4B;IAC5BC,gBAAgB;IAChBC,0BAA0B;IAC1BC,oBAAoB;IACpBC,yBAAyB;IACzBC,wBAAwB;IACxBC,yBAAyB;IACzBC,8BAA8B;IAC9BC,WAAW,EAAEC,iBAAiB;IAC9BC,qBAAqB;IACrBjC,iBAAiB;IACjB,GAAGkC;EACL,CAAC,GAAGlD,KAAK;EAET,MAAMmD,aAAa,GAAG/D,sBAAsB,CAC1CyB,MAAM,EACNa,KAAK,EACLuB,qBACF,CAAC;EAED,MAAM;IAAEvC,MAAM,GAAGyC;EAAc,CAAC,GAAGxD,UAAU,CAACyD,OAAO,CACnDJ,iBAAiB,IAAI,CAAC,CACxB,CAAc;EAEd,MAAM;IACJK,UAAU;IACVC,eAAe;IACfC,cAAc;IACdC,gBAAgB;IAChBC;EACF,CAAC,GAAG1C,oBAAoB,CACtBC,iBAAiB,EACjBH,MAAM,EACNO,QAAQ,CAACH,OAAO,EAChBG,QAAQ,CAACF,IAAI,EACbZ,WAAW,EACX6B,eAAe,GAAGjC,eAAe,GAAGG,SAAS,EAC7C,OAAOK,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGyC,aACxC,CAAC;EAED,MAAMrB,UAA4C,GAAGC,IAAI,GACpD/B,KAAK,IACJ+B,IAAI,CAAC;IACH,GAAG/B,KAAK;IACR0D,IAAI,EAAE9B,QAAQ;IACd+B,SAAS,EAAEzB,eAAe;IAC1B0B,kBAAkB,EAAErB,4BAA4B;IAChDsB,MAAM,EAAErB,gBAAgB;IACxBsB,gBAAgB,EAAErB,0BAA0B;IAC5CsB,OAAO,EAAEpC,QAAQ;IACjBqC,KAAK,EAAE7B,eAAe;IACtB8B,cAAc,EAAE3B,wBAAwB;IACxC4B,UAAU,EAAE,CAACX,cAAc,EAAEb,oBAAoB,CAAC;IAClDyB,aAAa,EAAErD,qBAAqB;IACpCsD,YAAY,EAAEvD,MAAM;IACpBP,WAAW;IACX+D,SAAS,EAAEC,OAAO,CAAC3C,QAAQ;EAC7B,CAAC,CAAC,GACJtB,SAAS;EAEb,MAAM2B,WAA8C,GAAGC,KAAK,GACvDjC,KAAK,IACJiC,KAAK,CAAC;IACJ,GAAGjC,KAAK;IACRqE,SAAS,EAAEC,OAAO,CAAC3C,QAAQ;EAC7B,CAAC,CAAC,GACJtB,SAAS;EAEb,MAAMwB,WAA8C,GAClD,OAAOL,KAAK,KAAK,UAAU,GACtBxB,KAAK,iBAAKF,IAAA,CAACP,WAAW;IAAA,GAAKS,KAAK;IAAEuE,QAAQ,EAAE/D;EAAkB,CAAE,CAAC,GACjER,KAAK,IAAKwB,KAAK,CAAC;IAAE,GAAGxB,KAAK;IAAEuE,QAAQ,EAAE/D;EAAkB,CAAC,CAAC;EAEjE,oBACEV,IAAA,CAACT,MAAM;IACLqC,KAAK,EAAEA,KAAM;IACbb,MAAM,EAAEA,MAAO;IACfgB,WAAW,EAAEA,WAAY;IACzBC,UAAU,EAAEA,UAAW;IACvBE,WAAW,EAAEA,WAAY;IACzBW,yBAAyB,EAAE,CAACU,UAAU,EAAEV,yBAAyB,CAAE;IACnEC,wBAAwB,EAAE,CAACU,eAAe,EAAEV,wBAAwB,CAAE;IACtEC,yBAAyB,EAAE,CAACW,gBAAgB,EAAEX,yBAAyB,CAAE;IACzET,2BAA2B,EAAEA,2BAA4B;IACzDU,8BAA8B,EAAE,CAC9BW,eAAe,EACfX,8BAA8B,CAC9B;IACFC,WAAW,EAAEC,iBAAkB;IAC/BC,qBAAqB,EAAEA,qBAAsB;IAAA,GACzCC;EAAI,CACT,CAAC;AAEN", "ignoreList": []}