{"version": 3, "names": ["React", "useLatestCallback", "deepFreeze", "createStore", "getInitialState", "listeners", "initialized", "state", "getState", "isBatching", "didUpdate", "setState", "newState", "for<PERSON>ach", "listener", "subscribe", "callback", "push", "index", "indexOf", "splice", "batchUpdates", "useSyncState", "store", "useRef", "current", "useSyncExternalStore", "useDebugValue", "pendingUpdatesRef", "scheduleUpdate", "flushUpdates", "pendingUpdates", "length", "update"], "sourceRoot": "../../src", "sources": ["useSyncState.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,UAAU,QAAQ,iBAAc;AAEzC,MAAMC,WAAW,GAAQC,eAAwB,IAAK;EACpD,MAAMC,SAAyB,GAAG,EAAE;EAEpC,IAAIC,WAAW,GAAG,KAAK;EACvB,IAAIC,KAAQ;EAEZ,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIF,WAAW,EAAE;MACf,OAAOC,KAAK;IACd;IAEAD,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAGL,UAAU,CAACE,eAAe,CAAC,CAAC,CAAC;IAErC,OAAOG,KAAK;EACd,CAAC;EAED,IAAIE,UAAU,GAAG,KAAK;EACtB,IAAIC,SAAS,GAAG,KAAK;EAErB,MAAMC,QAAQ,GAAIC,QAAW,IAAK;IAChCL,KAAK,GAAGL,UAAU,CAACU,QAAQ,CAAC;IAC5BF,SAAS,GAAG,IAAI;IAEhB,IAAI,CAACD,UAAU,EAAE;MACfJ,SAAS,CAACQ,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC;EAED,MAAMC,SAAS,GAAIC,QAAoB,IAAK;IAC1CX,SAAS,CAACY,IAAI,CAACD,QAAQ,CAAC;IAExB,OAAO,MAAM;MACX,MAAME,KAAK,GAAGb,SAAS,CAACc,OAAO,CAACH,QAAQ,CAAC;MAEzC,IAAIE,KAAK,GAAG,CAAC,CAAC,EAAE;QACdb,SAAS,CAACe,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC5B;IACF,CAAC;EACH,CAAC;EAED,MAAMG,YAAY,GAAIL,QAAoB,IAAK;IAC7CP,UAAU,GAAG,IAAI;IACjBO,QAAQ,CAAC,CAAC;IACVP,UAAU,GAAG,KAAK;IAElB,IAAIC,SAAS,EAAE;MACbA,SAAS,GAAG,KAAK;MACjBL,SAAS,CAACQ,OAAO,CAAEC,QAAQ,IAAKA,QAAQ,CAAC,CAAC,CAAC;IAC7C;EACF,CAAC;EAED,OAAO;IACLN,QAAQ;IACRG,QAAQ;IACRU,YAAY;IACZN;EACF,CAAC;AACH,CAAC;AAED,OAAO,SAASO,YAAYA,CAAIlB,eAAwB,EAAE;EACxD,MAAMmB,KAAK,GAAGvB,KAAK,CAACwB,MAAM,CAACrB,WAAW,CAACC,eAAe,CAAC,CAAC,CAACqB,OAAO;EAEhE,MAAMlB,KAAK,GAAGP,KAAK,CAAC0B,oBAAoB,CACtCH,KAAK,CAACR,SAAS,EACfQ,KAAK,CAACf,QAAQ,EACde,KAAK,CAACf,QACR,CAAC;EAEDR,KAAK,CAAC2B,aAAa,CAACpB,KAAK,CAAC;EAE1B,MAAMqB,iBAAiB,GAAG5B,KAAK,CAACwB,MAAM,CAAiB,EAAE,CAAC;EAE1D,MAAMK,cAAc,GAAG5B,iBAAiB,CAAEe,QAAoB,IAAK;IACjEY,iBAAiB,CAACH,OAAO,CAACR,IAAI,CAACD,QAAQ,CAAC;EAC1C,CAAC,CAAC;EAEF,MAAMc,YAAY,GAAG7B,iBAAiB,CAAC,MAAM;IAC3C,MAAM8B,cAAc,GAAGH,iBAAiB,CAACH,OAAO;IAEhDG,iBAAiB,CAACH,OAAO,GAAG,EAAE;IAE9B,IAAIM,cAAc,CAACC,MAAM,KAAK,CAAC,EAAE;MAC/BT,KAAK,CAACF,YAAY,CAAC,MAAM;QACvB;QACA,KAAK,MAAMY,MAAM,IAAIF,cAAc,EAAE;UACnCE,MAAM,CAAC,CAAC;QACV;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EAEF,OAAO;IACL1B,KAAK;IACLC,QAAQ,EAAEe,KAAK,CAACf,QAAQ;IACxBG,QAAQ,EAAEY,KAAK,CAACZ,QAAQ;IACxBkB,cAAc;IACdC;EACF,CAAC;AACH", "ignoreList": []}