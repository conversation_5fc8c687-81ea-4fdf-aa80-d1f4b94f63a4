{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "expo-camera": "^16.1.9", "expo-constants": "^17.1.6", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-media-library": "^17.1.7", "expo-permissions": "^14.4.0", "expo-secure-store": "^14.2.3", "express": "^5.1.0", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "redis": "^5.5.6", "uuid": "^11.0.3", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22.10.5", "nodemon": "^3.1.10", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}}