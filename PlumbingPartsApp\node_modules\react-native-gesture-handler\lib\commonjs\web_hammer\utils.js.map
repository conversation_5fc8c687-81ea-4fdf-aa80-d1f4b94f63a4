{"version": 3, "names": ["isnan", "v", "Number", "isNaN", "exports", "isValidNumber", "TEST_MIN_IF_NOT_NAN", "value", "limit", "VEC_LEN_SQ", "x", "y", "TEST_MAX_IF_NOT_NAN", "max", "fireAfterInterval", "method", "interval", "setTimeout"], "sourceRoot": "../../../src", "sources": ["web_hammer/utils.ts"], "mappings": ";;;;;;;;AAAA;AACO,MAAMA,KAAK,GAAIC,CAAU,IAAKC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC;;AAEpD;AAAAG,OAAA,CAAAJ,KAAA,GAAAA,KAAA;AACO,MAAMK,aAAa,GAAIJ,CAAU,IACtC,OAAOA,CAAC,KAAK,QAAQ,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,CAAC,CAAC;AAACG,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAErC,MAAMC,mBAAmB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAC9D,CAACR,KAAK,CAACQ,KAAK,CAAC,KACXA,KAAK,GAAG,CAAC,IAAID,KAAK,IAAIC,KAAK,IAAMA,KAAK,IAAI,CAAC,IAAID,KAAK,IAAIC,KAAM,CAAC;AAACJ,OAAA,CAAAE,mBAAA,GAAAA,mBAAA;AAC7D,MAAMG,UAAU,GAAGA,CAAC;EAAEC,CAAC,GAAG,CAAC;EAAEC,CAAC,GAAG;AAAE,CAAC,GAAG,CAAC,CAAC,KAAKD,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;AAACP,OAAA,CAAAK,UAAA,GAAAA,UAAA;AAC5D,MAAMG,mBAAmB,GAAGA,CAACL,KAAa,EAAEM,GAAW,KAC5D,CAACb,KAAK,CAACa,GAAG,CAAC,KAAMA,GAAG,GAAG,CAAC,IAAIN,KAAK,GAAGM,GAAG,IAAMA,GAAG,IAAI,CAAC,IAAIN,KAAK,GAAGM,GAAI,CAAC;AAACT,OAAA,CAAAQ,mBAAA,GAAAA,mBAAA;AAElE,SAASE,iBAAiBA,CAC/BC,MAAkB,EAClBC,QAA2B,EAC3B;EACA,IAAI,CAACA,QAAQ,EAAE;IACbD,MAAM,CAAC,CAAC;IACR,OAAO,IAAI;EACb;EACA,OAAOE,UAAU,CAAC,MAAMF,MAAM,CAAC,CAAC,EAAEC,QAAkB,CAAC;AACvD", "ignoreList": []}